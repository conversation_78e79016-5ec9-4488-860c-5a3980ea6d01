<?php

namespace Database\Seeders;

use App\Models\Continent;
use App\Models\Country;
use App\Models\Subregion;
use Google\Cloud\Translate\V3\TranslationServiceClient;
use Illuminate\Database\Seeder;

class WorldGeographiesTranslationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $config = ['credentials' => storage_path('app/application_default_credentials.json')];

        $translationClient = new TranslationServiceClient($config);

        // continents
        Continent::chunk(100, function ($continents) use ($translationClient)
        {
            foreach ($continents as $continent)
            {
                echo $continent->name . "\n";
                $content = $continent->name;
                $targetLanguage = 'el';
                $response = $translationClient->translateText(
                    [$content],
                    $targetLanguage,
                    TranslationServiceClient::locationName(config('services.google.cloud_translate.project_id'), 'global')
                );

                foreach ($response->getTranslations() as $key => $translation)
                {
                    $continent->update(['name' => $translation->getTranslatedText()]);
                    echo $translation->getTranslatedText() . "\n";
                }
            }
        });

        // subregions
        Subregion::chunk(100, function ($subregions) use ($translationClient)
        {
            foreach ($subregions as $subregion)
            {
                echo $subregion->name . "\n";

                $content = $subregion->name;
                $targetLanguage = 'el';
                $response = $translationClient->translateText(
                    [$content],
                    $targetLanguage,
                    TranslationServiceClient::locationName(config('services.google.cloud_translate.project_id'), 'global')
                );

                foreach ($response->getTranslations() as $key => $translation)
                {
                    $subregion->update(['name' => $translation->getTranslatedText()]);
                    echo $translation->getTranslatedText() . "\n";
                }
            }
        });

        // countries
        Country::chunk(100, function ($countries) use ($translationClient)
        {
            foreach ($countries as $country)
            {
                echo $country->name . "\n";

                $content = $country->name;
                $targetLanguage = 'el';
                $response = $translationClient->translateText(
                    [$content],
                    $targetLanguage,
                    TranslationServiceClient::locationName(config('services.google.cloud_translate.project_id'), 'global')
                );

                foreach ($response->getTranslations() as $key => $translation)
                {
                    $country->update(['name' => $translation->getTranslatedText()]);
                    echo $translation->getTranslatedText() . "\n";
                }
            }
        });

    }
}
